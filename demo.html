<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 图表管理演示</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .diagram-preview {
            max-height: 200px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }

        .mermaid {
            text-align: center;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">我的 Mermaid 图表</h1>
            <p class="text-gray-600">本地存储的图表管理系统演示</p>
        </header>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-2xl font-bold text-blue-600" id="total-count">0</div>
                <div class="text-sm text-gray-600">总图表数</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-2xl font-bold text-green-600" id="flowchart-count">0</div>
                <div class="text-sm text-gray-600">流程图</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-2xl font-bold text-purple-600" id="sequence-count">0</div>
                <div class="text-sm text-gray-600">时序图</div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="text-2xl font-bold text-orange-600" id="other-count">0</div>
                <div class="text-sm text-gray-600">其他类型</div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button onclick="showCreateDialog()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                新建图表
            </button>
            <button onclick="exportDiagrams()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                导出所有图表
            </button>
            <label class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 cursor-pointer">
                导入图表
                <input type="file" accept=".json" onchange="importDiagrams(event)" class="hidden">
            </label>
            <input type="text" id="search-input" placeholder="搜索图表..."
                class="px-4 py-2 border border-gray-300 rounded-lg" onkeyup="filterDiagrams()">
        </div>

        <!-- 图表网格 -->
        <div id="diagrams-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 图表卡片将在这里动态生成 -->
        </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <div id="diagram-dialog" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 id="dialog-title" class="text-xl font-bold mb-4">新建图表</h2>
            <form onsubmit="saveDiagram(event)">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                    <input type="text" id="diagram-title" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">类型</label>
                    <select id="diagram-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        <option value="flowchart">流程图</option>
                        <option value="sequence">时序图</option>
                        <option value="gantt">甘特图</option>
                        <option value="pie">饼图</option>
                        <option value="class">类图</option>
                        <option value="state">状态图</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">标签（用逗号分隔）</label>
                    <input type="text" id="diagram-tags" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mermaid 代码</label>
                    <textarea id="diagram-code" rows="10" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm"
                        placeholder="graph TD&#10;    A[开始] --> B[处理]&#10;    B --> C[结束]"></textarea>
                </div>
                <div class="flex justify-end gap-4">
                    <button type="button" onclick="hideDialog()"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose'
        });

        // 图表存储管理
        class DiagramStorage {
            constructor() {
                this.storageKey = 'mermaid-diagrams';
                this.diagrams = this.loadDiagrams();
            }

            loadDiagrams() {
                try {
                    const data = localStorage.getItem(this.storageKey);
                    return data ? JSON.parse(data) : [];
                } catch (error) {
                    console.error('加载图表失败:', error);
                    return [];
                }
            }

            saveDiagrams() {
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(this.diagrams));
                } catch (error) {
                    console.error('保存图表失败:', error);
                }
            }

            addDiagram(diagram) {
                diagram.id = Date.now().toString();
                diagram.createdAt = new Date().toISOString();
                diagram.updatedAt = diagram.createdAt;
                this.diagrams.unshift(diagram);
                this.saveDiagrams();
                return diagram;
            }

            updateDiagram(id, updates) {
                const index = this.diagrams.findIndex(d => d.id === id);
                if (index !== -1) {
                    this.diagrams[index] = {
                        ...this.diagrams[index],
                        ...updates,
                        updatedAt: new Date().toISOString()
                    };
                    this.saveDiagrams();
                    return this.diagrams[index];
                }
                return null;
            }

            deleteDiagram(id) {
                this.diagrams = this.diagrams.filter(d => d.id !== id);
                this.saveDiagrams();
            }

            getDiagrams() {
                return this.diagrams;
            }

            exportDiagrams() {
                return JSON.stringify(this.diagrams, null, 2);
            }

            importDiagrams(jsonData) {
                try {
                    const imported = JSON.parse(jsonData);
                    if (Array.isArray(imported)) {
                        // 为导入的图表生成新的ID以避免冲突
                        imported.forEach(diagram => {
                            diagram.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
                            diagram.importedAt = new Date().toISOString();
                        });
                        this.diagrams = [...imported, ...this.diagrams];
                        this.saveDiagrams();
                        return imported.length;
                    }
                } catch (error) {
                    console.error('导入图表失败:', error);
                    throw new Error('无效的JSON格式');
                }
                return 0;
            }
        }

        // 全局变量
        const storage = new DiagramStorage();
        let currentEditingId = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            renderDiagrams();
            updateStatistics();

            // 添加一些示例数据（如果没有数据的话）
            if (storage.getDiagrams().length === 0) {
                addSampleDiagrams();
            }
        });

        // 添加示例图表
        function addSampleDiagrams() {
            const samples = [
                {
                    title: '简单流程图',
                    type: 'flowchart',
                    tags: ['示例', '流程'],
                    code: `graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E`
                },
                {
                    title: '用户登录时序图',
                    type: 'sequence',
                    tags: ['示例', '登录', '时序'],
                    code: `sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库

    U->>F: 输入用户名密码
    F->>B: 发送登录请求
    B->>D: 验证用户信息
    D-->>B: 返回验证结果
    B-->>F: 返回登录状态
    F-->>U: 显示登录结果`
                }
            ];

            samples.forEach(sample => storage.addDiagram(sample));
            renderDiagrams();
            updateStatistics();
        }

        // 渲染图表网格
        function renderDiagrams(diagramsToRender = null) {
            const diagrams = diagramsToRender || storage.getDiagrams();
            const grid = document.getElementById('diagrams-grid');

            if (diagrams.length === 0) {
                grid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 text-lg mb-4">暂无图表</div>
                        <button onclick="showCreateDialog()"
                                class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                            创建第一个图表
                        </button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = diagrams.map(diagram => createDiagramCard(diagram)).join('');

            // 渲染所有图表预览
            diagrams.forEach(diagram => {
                renderDiagramPreview(diagram.id, diagram.code);
            });
        }

        // 创建图表卡片HTML
        function createDiagramCard(diagram) {
            const tags = Array.isArray(diagram.tags) ? diagram.tags :
                (diagram.tags ? diagram.tags.split(',').map(t => t.trim()) : []);

            return `
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="diagram-preview p-4">
                        <div id="preview-${diagram.id}" class="mermaid"></div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-semibold text-lg text-gray-900 truncate">${diagram.title}</h3>
                            <div class="flex gap-2 ml-2">
                                <button onclick="editDiagram('${diagram.id}')"
                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                    编辑
                                </button>
                                <button onclick="deleteDiagram('${diagram.id}')"
                                        class="text-red-600 hover:text-red-800 text-sm">
                                    删除
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 mb-2">
                            <span class="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                                ${getTypeDisplayName(diagram.type)}
                            </span>
                            <span class="text-xs text-gray-500">
                                ${formatDate(diagram.createdAt)}
                            </span>
                        </div>
                        ${tags.length > 0 ? `
                            <div class="flex flex-wrap gap-1">
                                ${tags.map(tag => `
                                    <span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                        ${tag}
                                    </span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 渲染图表预览
        async function renderDiagramPreview(id, code) {
            const element = document.getElementById(`preview-${id}`);
            if (!element) return;

            try {
                element.innerHTML = code;
                await mermaid.run({
                    nodes: [element]
                });
            } catch (error) {
                console.error('渲染图表预览失败:', error);
                element.innerHTML = `<div class="text-red-500 text-sm p-4">预览渲染失败</div>`;
            }
        }

        // 更新统计信息
        function updateStatistics() {
            const diagrams = storage.getDiagrams();
            const stats = {
                total: diagrams.length,
                flowchart: diagrams.filter(d => d.type === 'flowchart').length,
                sequence: diagrams.filter(d => d.type === 'sequence').length,
                other: diagrams.filter(d => !['flowchart', 'sequence'].includes(d.type)).length
            };

            document.getElementById('total-count').textContent = stats.total;
            document.getElementById('flowchart-count').textContent = stats.flowchart;
            document.getElementById('sequence-count').textContent = stats.sequence;
            document.getElementById('other-count').textContent = stats.other;
        }

        // 工具函数
        function getTypeDisplayName(type) {
            const typeNames = {
                'flowchart': '流程图',
                'sequence': '时序图',
                'gantt': '甘特图',
                'pie': '饼图',
                'class': '类图',
                'state': '状态图'
            };
            return typeNames[type] || type;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // 对话框操作
        function showCreateDialog() {
            currentEditingId = null;
            document.getElementById('dialog-title').textContent = '新建图表';
            document.getElementById('diagram-title').value = '';
            document.getElementById('diagram-type').value = 'flowchart';
            document.getElementById('diagram-tags').value = '';
            document.getElementById('diagram-code').value = 'graph TD\n    A[开始] --> B[处理]\n    B --> C[结束]';
            document.getElementById('diagram-dialog').classList.remove('hidden');
            document.getElementById('diagram-dialog').classList.add('flex');
        }

        function hideDialog() {
            document.getElementById('diagram-dialog').classList.add('hidden');
            document.getElementById('diagram-dialog').classList.remove('flex');
        }

        function editDiagram(id) {
            const diagram = storage.getDiagrams().find(d => d.id === id);
            if (!diagram) return;

            currentEditingId = id;
            document.getElementById('dialog-title').textContent = '编辑图表';
            document.getElementById('diagram-title').value = diagram.title;
            document.getElementById('diagram-type').value = diagram.type;
            document.getElementById('diagram-tags').value = Array.isArray(diagram.tags) ?
                diagram.tags.join(', ') : (diagram.tags || '');
            document.getElementById('diagram-code').value = diagram.code;
            document.getElementById('diagram-dialog').classList.remove('hidden');
            document.getElementById('diagram-dialog').classList.add('flex');
        }

        function saveDiagram(event) {
            event.preventDefault();

            const title = document.getElementById('diagram-title').value.trim();
            const type = document.getElementById('diagram-type').value;
            const tags = document.getElementById('diagram-tags').value
                .split(',').map(t => t.trim()).filter(t => t);
            const code = document.getElementById('diagram-code').value.trim();

            if (!title || !code) {
                alert('请填写标题和代码');
                return;
            }

            const diagramData = { title, type, tags, code };

            if (currentEditingId) {
                storage.updateDiagram(currentEditingId, diagramData);
            } else {
                storage.addDiagram(diagramData);
            }

            hideDialog();
            renderDiagrams();
            updateStatistics();
        }

        function deleteDiagram(id) {
            if (confirm('确定要删除这个图表吗？')) {
                storage.deleteDiagram(id);
                renderDiagrams();
                updateStatistics();
            }
        }

        // 搜索和过滤
        function filterDiagrams() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const allDiagrams = storage.getDiagrams();

            if (!searchTerm) {
                renderDiagrams();
                return;
            }

            const filtered = allDiagrams.filter(diagram => {
                const titleMatch = diagram.title.toLowerCase().includes(searchTerm);
                const typeMatch = getTypeDisplayName(diagram.type).toLowerCase().includes(searchTerm);
                const tagsMatch = Array.isArray(diagram.tags) ?
                    diagram.tags.some(tag => tag.toLowerCase().includes(searchTerm)) :
                    (diagram.tags && diagram.tags.toLowerCase().includes(searchTerm));

                return titleMatch || typeMatch || tagsMatch;
            });

            renderDiagrams(filtered);
        }

        // 导入导出功能
        function exportDiagrams() {
            const data = storage.exportDiagrams();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `mermaid-diagrams-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function importDiagrams(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const count = storage.importDiagrams(e.target.result);
                    alert(`成功导入 ${count} 个图表`);
                    renderDiagrams();
                    updateStatistics();
                } catch (error) {
                    alert('导入失败: ' + error.message);
                }
            };
            reader.readAsText(file);

            // 清空文件输入
            event.target.value = '';
        }

        // 键盘快捷键
        document.addEventListener('keydown', function (e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'n') {
                    e.preventDefault();
                    showCreateDialog();
                } else if (e.key === 's') {
                    e.preventDefault();
                    exportDiagrams();
                }
            }

            if (e.key === 'Escape') {
                hideDialog();
            }
        });
    </script>
</body>

</html>