<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 图表管理器</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .diagram-preview {
            min-height: 150px;
            max-height: 400px;
            overflow: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
            padding: 16px;
        }

        .mermaid {
            text-align: center;
            font-size: 12px;
        }

        .mermaid svg {
            max-width: 100%;
            height: auto;
        }

        /* 确保图表在卡片中正确显示 */
        .diagram-card {
            break-inside: avoid;
        }

        /* 改善对话框中的代码编辑器 */
        #diagram-code {
            font-family: 'Monaco', '<PERSON>lo', 'Ubuntu Mono', monospace;
            line-height: 1.4;
        }

        /* 全屏预览样式 */
        .fullscreen-preview {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .fullscreen-content {
            background: white;
            width: 100vw;
            height: 100vh;
            overflow: auto;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .fullscreen-header {
            position: sticky;
            top: 0;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
            flex-shrink: 0;
        }

        .fullscreen-diagram {
            flex: 1;
            padding: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: auto;
        }

        .fullscreen-diagram .mermaid {
            font-size: 16px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-diagram .mermaid svg {
            max-width: calc(100vw - 40px);
            max-height: calc(100vh - 120px);
            width: auto;
            height: auto;
        }

        .close-fullscreen {
            background: #f3f4f6;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .close-fullscreen:hover {
            background: #e5e7eb;
            color: #374151;
        }

        /* 预览区域添加点击提示 */
        .diagram-preview {
            cursor: pointer;
            transition: all 0.2s;
        }

        .diagram-preview:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 头部 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Mermaid 图表管理器</h1>
            <p class="text-gray-600">创建、编辑和管理您的 Mermaid 图表</p>
        </div>

        <!-- 操作栏 -->
        <div class="mb-6 flex flex-wrap gap-4 items-center justify-between">
            <div class="flex gap-4">
                <button onclick="showCreateDialog()"
                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    新建图表
                </button>
                <button onclick="exportDiagrams()"
                    class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    导出图表
                </button>
                <label
                    class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors cursor-pointer">
                    导入图表
                    <input type="file" accept=".json" onchange="importDiagrams(event)" class="hidden">
                </label>
            </div>
            <div class="flex-1 max-w-md">
                <input type="text" id="search-input" placeholder="搜索图表..." onkeyup="filterDiagrams()"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="text-2xl font-bold text-blue-600" id="total-count">0</div>
                <div class="text-sm text-gray-600">总图表</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="text-2xl font-bold text-green-600" id="flowchart-count">0</div>
                <div class="text-sm text-gray-600">流程图</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="text-2xl font-bold text-purple-600" id="sequence-count">0</div>
                <div class="text-sm text-gray-600">时序图</div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="text-2xl font-bold text-orange-600" id="other-count">0</div>
                <div class="text-sm text-gray-600">其他</div>
            </div>
        </div>

        <!-- 图表网格 -->
        <div id="diagrams-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 图表卡片将在这里动态生成 -->
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="text-center py-12 hidden">
            <div class="text-gray-400 text-6xl mb-4">📊</div>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">还没有图表</h3>
            <p class="text-gray-500 mb-4">创建您的第一个 Mermaid 图表开始使用</p>
            <button onclick="showCreateDialog()"
                class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                创建图表
            </button>
        </div>
    </div>

    <!-- 全屏预览 -->
    <div id="fullscreen-preview" class="fullscreen-preview hidden">
        <div class="fullscreen-content">
            <div class="fullscreen-header">
                <div>
                    <h2 id="fullscreen-title" class="text-xl font-bold text-gray-900"></h2>
                    <p id="fullscreen-type" class="text-sm text-gray-600"></p>
                </div>
                <button class="close-fullscreen" onclick="closeFullscreen()" title="关闭 (ESC)">
                    ×
                </button>
            </div>
            <div class="fullscreen-diagram">
                <div id="fullscreen-mermaid" class="mermaid"></div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <div id="diagram-dialog" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto mx-4">
            <h2 id="dialog-title" class="text-xl font-bold mb-4">新建图表</h2>
            <form onsubmit="saveDiagram(event)">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                        <input type="text" id="diagram-title" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">类型</label>
                        <select id="diagram-type"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="flowchart">流程图</option>
                            <option value="sequence">时序图</option>
                            <option value="gantt">甘特图</option>
                            <option value="pie">饼图</option>
                            <option value="class">类图</option>
                            <option value="state">状态图</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">标签 (用逗号分隔)</label>
                    <input type="text" id="diagram-tags" placeholder="例如: 工作流, 设计, 文档"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mermaid 代码</label>
                    <textarea id="diagram-code" rows="12" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="graph TD&#10;    A[开始] --> B[处理]&#10;    B --> C[结束]"></textarea>
                </div>
                <div class="flex justify-end gap-3">
                    <button type="button" onclick="hideDialog()"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose'
        });

        // 全局变量
        let storage;
        let currentEditingId = null;

        // 图表存储管理
        class DiagramStorage {
            constructor() {
                this.storageKey = 'mermaid-diagrams';
                this.diagrams = this.loadDiagrams();
            }

            loadDiagrams() {
                try {
                    const data = localStorage.getItem(this.storageKey);
                    return data ? JSON.parse(data) : [];
                } catch (error) {
                    console.error('加载图表失败:', error);
                    return [];
                }
            }

            saveDiagrams() {
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(this.diagrams));
                } catch (error) {
                    console.error('保存图表失败:', error);
                }
            }

            addDiagram(diagram) {
                diagram.id = Date.now().toString();
                diagram.createdAt = new Date().toISOString();
                diagram.updatedAt = diagram.createdAt;
                this.diagrams.unshift(diagram);
                this.saveDiagrams();
                return diagram;
            }

            updateDiagram(id, updates) {
                const index = this.diagrams.findIndex(d => d.id === id);
                if (index !== -1) {
                    this.diagrams[index] = {
                        ...this.diagrams[index],
                        ...updates,
                        updatedAt: new Date().toISOString()
                    };
                    this.saveDiagrams();
                    return true;
                }
                return false;
            }

            deleteDiagram(id) {
                const initialLength = this.diagrams.length;
                this.diagrams = this.diagrams.filter(d => d.id !== id);
                if (this.diagrams.length < initialLength) {
                    this.saveDiagrams();
                    return true;
                }
                return false;
            }

            getDiagrams() {
                return this.diagrams;
            }

            exportDiagrams() {
                const exportData = {
                    version: '1.0',
                    exportDate: new Date().toISOString(),
                    diagrams: this.diagrams
                };
                return JSON.stringify(exportData, null, 2);
            }

            importDiagrams(jsonString) {
                try {
                    const data = JSON.parse(jsonString);
                    let diagramsToImport = [];

                    if (Array.isArray(data)) {
                        diagramsToImport = data;
                    } else if (data.diagrams && Array.isArray(data.diagrams)) {
                        diagramsToImport = data.diagrams;
                    } else {
                        throw new Error('Invalid format');
                    }

                    const validDiagrams = diagramsToImport.filter(diagram =>
                        diagram &&
                        typeof diagram.title === 'string' &&
                        typeof diagram.code === 'string'
                    );

                    if (validDiagrams.length === 0) {
                        throw new Error('No valid diagrams found');
                    }

                    const existingIds = new Set(this.diagrams.map(d => d.id));
                    const newDiagrams = validDiagrams.map(diagram => {
                        const id = diagram.id && !existingIds.has(diagram.id) ?
                            diagram.id : Date.now().toString() + Math.random().toString(36).substr(2, 9);

                        return {
                            ...diagram,
                            id,
                            createdAt: diagram.createdAt || new Date().toISOString(),
                            updatedAt: diagram.updatedAt || new Date().toISOString()
                        };
                    });

                    this.diagrams = [...newDiagrams, ...this.diagrams];
                    this.saveDiagrams();
                    return newDiagrams.length;
                } catch (error) {
                    console.error('Import failed:', error);
                    throw new Error('导入失败，请检查文件格式');
                }
            }
        }

        // 添加示例图表
        function addSampleDiagrams() {
            const samples = [
                {
                    title: '简单流程图',
                    type: 'flowchart',
                    tags: ['示例', '流程'],
                    code: `graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E`
                },
                {
                    title: '用户登录时序图',
                    type: 'sequence',
                    tags: ['示例', '登录', '时序'],
                    code: `sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 输入用户名密码
    F->>B: 发送登录请求
    B->>D: 验证用户信息
    D-->>B: 返回验证结果
    B-->>F: 返回登录状态
    F-->>U: 显示登录结果`
                },
                {
                    title: '项目甘特图',
                    type: 'gantt',
                    tags: ['示例', '项目管理'],
                    code: `gantt
    title 项目开发计划
    dateFormat  YYYY-MM-DD
    section 设计阶段
    需求分析      :done,    des1, 2024-01-01,2024-01-05
    UI设计       :done,    des2, 2024-01-06,2024-01-10
    section 开发阶段
    前端开发     :active,  dev1, 2024-01-11,2024-01-25
    后端开发     :         dev2, 2024-01-15,2024-01-30
    section 测试阶段
    功能测试     :         test1, 2024-01-26,2024-02-05
    上线部署     :         deploy, 2024-02-06,2024-02-08`
                }
            ];

            samples.forEach(sample => storage.addDiagram(sample));
            renderDiagrams();
            updateStatistics();
        }

        // 初始化应用
        function initApp() {
            storage = new DiagramStorage();

            // 如果没有图表，添加示例
            if (storage.getDiagrams().length === 0) {
                addSampleDiagrams();
            } else {
                renderDiagrams();
                updateStatistics();
            }
        }

        // 渲染图表列表
        function renderDiagrams(diagramsToRender = null) {
            const diagrams = diagramsToRender || storage.getDiagrams();
            const grid = document.getElementById('diagrams-grid');
            const emptyState = document.getElementById('empty-state');

            if (diagrams.length === 0) {
                grid.innerHTML = '';
                emptyState.classList.remove('hidden');
                return;
            }

            emptyState.classList.add('hidden');

            grid.innerHTML = diagrams.map(diagram => {
                const tags = Array.isArray(diagram.tags) ?
                    diagram.tags.map(tag => `<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">${tag}</span>`).join(' ') :
                    '';

                return `
                <div class="diagram-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="diagram-preview" onclick="showFullscreen('${diagram.id}')" title="点击查看全屏预览">
                        <div id="preview-${diagram.id}" class="mermaid"></div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-semibold text-gray-900 truncate">${diagram.title}</h3>
                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded ml-2">${getTypeDisplayName(diagram.type)}</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">${formatDate(diagram.createdAt)}</p>
                        <div class="flex flex-wrap gap-1 mb-3">
                            ${tags}
                        </div>
                        <div class="flex justify-end gap-2">
                            <button onclick="editDiagram('${diagram.id}')" 
                                class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                            <button onclick="deleteDiagram('${diagram.id}')" 
                                class="text-red-600 hover:text-red-800 text-sm">删除</button>
                        </div>
                    </div>
                </div>
            `;
            }).join('');

            // 渲染所有图表预览
            diagrams.forEach(diagram => {
                renderDiagramPreview(diagram.id, diagram.code);
            });
        }

        // 渲染图表预览
        async function renderDiagramPreview(id, code) {
            const element = document.getElementById(`preview-${id}`);
            if (!element) return;

            try {
                // 清空元素内容
                element.innerHTML = '';
                element.removeAttribute('data-processed');

                // 设置图表代码
                element.textContent = code;
                element.className = 'mermaid';

                // 渲染图表
                await mermaid.run({
                    nodes: [element]
                });

                // 确保SVG适应容器
                const svg = element.querySelector('svg');
                if (svg) {
                    svg.style.maxWidth = '100%';
                    svg.style.height = 'auto';
                }
            } catch (error) {
                console.error('渲染图表预览失败:', error);
                element.innerHTML = `<div class="text-red-500 text-sm p-4 text-center">
                    <div class="mb-2">⚠️ 预览渲染失败</div>
                    <div class="text-xs">${error.message || '图表语法可能有误'}</div>
                </div>`;
            }
        }

        // 更新统计信息
        function updateStatistics() {
            const diagrams = storage.getDiagrams();
            const stats = {
                total: diagrams.length,
                flowchart: diagrams.filter(d => d.type === 'flowchart').length,
                sequence: diagrams.filter(d => d.type === 'sequence').length,
                other: diagrams.filter(d => !['flowchart', 'sequence'].includes(d.type)).length
            };

            document.getElementById('total-count').textContent = stats.total;
            document.getElementById('flowchart-count').textContent = stats.flowchart;
            document.getElementById('sequence-count').textContent = stats.sequence;
            document.getElementById('other-count').textContent = stats.other;
        }

        // 工具函数
        function getTypeDisplayName(type) {
            const typeNames = {
                'flowchart': '流程图',
                'sequence': '时序图',
                'gantt': '甘特图',
                'pie': '饼图',
                'class': '类图',
                'state': '状态图'
            };
            return typeNames[type] || type;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // 对话框操作
        function showCreateDialog() {
            currentEditingId = null;
            document.getElementById('dialog-title').textContent = '新建图表';
            document.getElementById('diagram-title').value = '';
            document.getElementById('diagram-type').value = 'flowchart';
            document.getElementById('diagram-tags').value = '';
            document.getElementById('diagram-code').value = 'graph TD\n    A[开始] --> B[处理]\n    B --> C[结束]';
            document.getElementById('diagram-dialog').classList.remove('hidden');
            document.getElementById('diagram-dialog').classList.add('flex');
        }

        function hideDialog() {
            document.getElementById('diagram-dialog').classList.add('hidden');
            document.getElementById('diagram-dialog').classList.remove('flex');
        }

        function editDiagram(id) {
            const diagram = storage.getDiagrams().find(d => d.id === id);
            if (!diagram) return;

            currentEditingId = id;
            document.getElementById('dialog-title').textContent = '编辑图表';
            document.getElementById('diagram-title').value = diagram.title;
            document.getElementById('diagram-type').value = diagram.type;
            document.getElementById('diagram-tags').value = Array.isArray(diagram.tags) ?
                diagram.tags.join(', ') : (diagram.tags || '');
            document.getElementById('diagram-code').value = diagram.code;
            document.getElementById('diagram-dialog').classList.remove('hidden');
            document.getElementById('diagram-dialog').classList.add('flex');
        }

        function saveDiagram(event) {
            event.preventDefault();

            const title = document.getElementById('diagram-title').value.trim();
            const type = document.getElementById('diagram-type').value;
            const tags = document.getElementById('diagram-tags').value
                .split(',').map(t => t.trim()).filter(t => t);
            const code = document.getElementById('diagram-code').value.trim();

            if (!title || !code) {
                alert('请填写标题和代码');
                return;
            }

            const diagramData = { title, type, tags, code };

            if (currentEditingId) {
                storage.updateDiagram(currentEditingId, diagramData);
            } else {
                storage.addDiagram(diagramData);
            }

            hideDialog();
            renderDiagrams();
            updateStatistics();
        }

        function deleteDiagram(id) {
            if (confirm('确定要删除这个图表吗？')) {
                storage.deleteDiagram(id);
                renderDiagrams();
                updateStatistics();
            }
        }

        // 搜索和过滤
        function filterDiagrams() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const allDiagrams = storage.getDiagrams();

            if (!searchTerm) {
                renderDiagrams();
                return;
            }

            const filtered = allDiagrams.filter(diagram => {
                const titleMatch = diagram.title.toLowerCase().includes(searchTerm);
                const typeMatch = getTypeDisplayName(diagram.type).toLowerCase().includes(searchTerm);
                const tagsMatch = Array.isArray(diagram.tags) ?
                    diagram.tags.some(tag => tag.toLowerCase().includes(searchTerm)) :
                    (diagram.tags && diagram.tags.toLowerCase().includes(searchTerm));

                return titleMatch || typeMatch || tagsMatch;
            });

            renderDiagrams(filtered);
        }

        // 导入导出功能
        function exportDiagrams() {
            const data = storage.exportDiagrams();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `mermaid-diagrams-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function importDiagrams(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const count = storage.importDiagrams(e.target.result);
                    alert(`成功导入 ${count} 个图表`);
                    renderDiagrams();
                    updateStatistics();
                } catch (error) {
                    alert('导入失败: ' + error.message);
                }
            };
            reader.readAsText(file);

            // 清空文件输入
            event.target.value = '';
        }

        // 全屏预览功能
        function showFullscreen(diagramId) {
            const diagram = storage.getDiagrams().find(d => d.id === diagramId);
            if (!diagram) return;

            // 设置标题和类型
            document.getElementById('fullscreen-title').textContent = diagram.title;
            document.getElementById('fullscreen-type').textContent = getTypeDisplayName(diagram.type);

            // 渲染全屏图表
            renderFullscreenDiagram(diagram.code);

            // 显示全屏预览
            document.getElementById('fullscreen-preview').classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        function closeFullscreen() {
            document.getElementById('fullscreen-preview').classList.add('hidden');
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        async function renderFullscreenDiagram(code) {
            const element = document.getElementById('fullscreen-mermaid');
            if (!element) return;

            try {
                // 清空元素内容
                element.innerHTML = '';
                element.removeAttribute('data-processed');

                // 设置图表代码
                element.textContent = code;
                element.className = 'mermaid';

                // 渲染图表
                await mermaid.run({
                    nodes: [element]
                });

                // 确保SVG充分利用全屏空间
                const svg = element.querySelector('svg');
                if (svg) {
                    // 移除原有的宽高限制
                    svg.removeAttribute('width');
                    svg.removeAttribute('height');

                    // 设置新的样式以充分利用空间
                    svg.style.maxWidth = 'calc(100vw - 40px)';
                    svg.style.maxHeight = 'calc(100vh - 120px)';
                    svg.style.width = 'auto';
                    svg.style.height = 'auto';

                    // 如果图表很小，允许它放大
                    const svgRect = svg.getBoundingClientRect();
                    const containerWidth = window.innerWidth - 40;
                    const containerHeight = window.innerHeight - 120;

                    if (svgRect.width < containerWidth * 0.5 && svgRect.height < containerHeight * 0.5) {
                        svg.style.transform = 'scale(1.5)';
                    }
                }
            } catch (error) {
                console.error('渲染全屏图表失败:', error);
                element.innerHTML = `<div class="text-red-500 text-center p-8">
                    <div class="text-2xl mb-4">⚠️ 渲染失败</div>
                    <div class="text-lg">${error.message || '图表语法可能有误'}</div>
                    <div class="mt-4 text-sm text-gray-600">请检查图表代码是否正确</div>
                </div>`;
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function (e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'n') {
                    e.preventDefault();
                    showCreateDialog();
                } else if (e.key === 's') {
                    e.preventDefault();
                    exportDiagrams();
                }
            }

            if (e.key === 'Escape') {
                // 优先关闭全屏预览，然后是对话框
                if (!document.getElementById('fullscreen-preview').classList.contains('hidden')) {
                    closeFullscreen();
                } else {
                    hideDialog();
                }
            }
        });

        // 点击背景关闭全屏预览
        document.getElementById('fullscreen-preview').addEventListener('click', function (e) {
            if (e.target === this) {
                closeFullscreen();
            }
        });

        // 页面加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>

</html>