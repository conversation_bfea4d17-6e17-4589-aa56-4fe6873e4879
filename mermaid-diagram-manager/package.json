{"name": "mermaid-diagram-manager", "version": "1.0.0", "description": "一个基于浏览器的本地 Mermaid 图表管理工具", "main": "index.html", "scripts": {"start": "npx serve .", "dev": "npx serve . --listen 3000", "serve": "python -m http.server 8000"}, "keywords": ["mermaid", "diagram", "chart", "visualization", "flowchart", "sequence", "gantt", "管理工具", "图表"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/mermaid-diagram-manager.git"}, "bugs": {"url": "https://github.com/yourusername/mermaid-diagram-manager/issues"}, "homepage": "https://github.com/yourusername/mermaid-diagram-manager#readme", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}}