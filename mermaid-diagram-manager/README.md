# Mermaid 图表管理器

一个基于浏览器的本地 Mermaid 图表管理工具，支持创建、编辑、预览和管理 Mermaid 图表，数据完全存储在浏览器本地。

## 功能特性

### 🎯 核心功能
- **本地存储管理** - 使用 localStorage 持久化存储图表数据
- **图表 CRUD 操作** - 创建、读取、更新、删除图表
- **实时预览** - 每个图表卡片都显示 Mermaid 图表的实时预览
- **全屏预览** - 点击图表预览可全屏查看完整图表
- **统计信息** - 显示总图表数、流程图数量、时序图数量等统计

### 🔧 用户界面
- **响应式设计** - 使用 Tailwind CSS 实现美观的响应式界面
- **图表网格** - 卡片式布局展示所有图表
- **创建/编辑对话框** - 模态对话框用于创建和编辑图表
- **搜索功能** - 支持按标题、类型、标签搜索图表

### 📊 图表管理
- **多种图表类型** - 支持流程图、时序图、甘特图、饼图、类图、状态图
- **标签系统** - 为图表添加标签便于分类和搜索
- **时间戳** - 记录创建和更新时间
- **图表预览** - 实时渲染 Mermaid 图表预览

### 💾 导入导出
- **JSON 导出** - 将所有图表导出为 JSON 文件
- **批量导入** - 从 JSON 文件导入图表数据
- **数据备份** - 支持完整的数据备份和恢复

### ⌨️ 用户体验
- **键盘快捷键** - Ctrl+N 新建图表，Ctrl+S 导出，ESC 关闭对话框
- **示例数据** - 首次使用时自动添加示例图表
- **错误处理** - 完善的错误处理和用户提示
- **确认对话框** - 删除操作需要用户确认

## 数据存储

### 存储位置
- **浏览器 localStorage** - 所有图表数据存储在浏览器的 localStorage 中
- **存储键名** - `mermaid-diagrams`
- **数据格式** - JSON 格式存储图表数组

### 数据结构
```json
{
  "id": "唯一标识符",
  "title": "图表标题",
  "type": "图表类型 (flowchart, sequence, gantt, pie, class, state)",
  "code": "Mermaid 图表代码",
  "tags": ["标签1", "标签2"],
  "createdAt": "创建时间 (ISO 字符串)",
  "updatedAt": "更新时间 (ISO 字符串)"
}
```

### 数据持久化
- **自动保存** - 每次创建、编辑、删除操作都会自动保存到 localStorage
- **跨会话持久化** - 关闭浏览器后重新打开，数据依然存在
- **导出备份** - 可以导出 JSON 文件作为备份
- **导入恢复** - 可以从 JSON 文件恢复数据

## 快速开始

### 方法一：直接使用
1. 下载 `index.html` 文件
2. 在浏览器中打开该文件
3. 开始创建和管理您的 Mermaid 图表

### 方法二：本地服务器
```bash
# 克隆项目
git clone <repository-url>
cd mermaid-diagram-manager

# 使用 Python 启动本地服务器
python -m http.server 8000

# 或使用 Node.js
npx serve .

# 在浏览器中访问
open http://localhost:8000
```

## 使用指南

### 创建图表
1. 点击"新建图表"按钮
2. 填写图表标题
3. 选择图表类型
4. 输入 Mermaid 代码
5. 添加标签（可选）
6. 点击"保存"

### 编辑图表
1. 点击图表卡片上的"编辑"按钮
2. 修改图表信息
3. 点击"保存"保存更改

### 全屏预览
1. 点击图表预览区域
2. 在全屏模式下查看完整图表
3. 按 ESC 键或点击关闭按钮退出全屏

### 搜索图表
1. 在搜索框中输入关键词
2. 支持搜索标题、类型、标签
3. 实时过滤显示结果

### 导入导出
- **导出**：点击"导出图表"按钮，下载 JSON 文件
- **导入**：点击"导入图表"按钮，选择 JSON 文件

## 键盘快捷键

- `Ctrl + N` - 新建图表
- `Ctrl + S` - 导出图表
- `ESC` - 关闭对话框/全屏预览

## 技术栈

- **前端框架** - 纯 HTML/CSS/JavaScript
- **图表渲染** - Mermaid.js v10.6.1
- **样式框架** - Tailwind CSS
- **数据存储** - Browser localStorage
- **模块化** - ES6 Classes

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 项目结构

```
mermaid-diagram-manager/
├── index.html          # 主应用文件
├── README.md          # 项目文档
├── LICENSE            # 开源协议
└── screenshots/       # 截图目录
    ├── main-view.png
    ├── fullscreen.png
    └── edit-dialog.png
```

## 开发

### 本地开发
项目使用纯 HTML/CSS/JavaScript 开发，无需构建步骤：

1. 直接编辑 `index.html` 文件
2. 在浏览器中刷新查看更改
3. 使用浏览器开发者工具调试

### 代码结构
- **DiagramStorage 类** - 处理数据存储和管理
- **UI 函数** - 处理用户界面交互
- **Mermaid 集成** - 图表渲染和预览

## 贡献

欢迎提交 Issue 和 Pull Request！

### 开发指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 部署到 GitHub Pages

1. **创建 GitHub 仓库**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/mermaid-diagram-manager.git
   git push -u origin main
   ```

2. **启用 GitHub Pages**
   - 进入仓库设置 (Settings)
   - 找到 Pages 选项
   - Source 选择 "Deploy from a branch"
   - Branch 选择 "main" 和 "/ (root)"
   - 点击 Save

3. **访问您的应用**
   - 几分钟后访问: `https://yourusername.github.io/mermaid-diagram-manager/`

## 本地开发服务器

### 使用 Node.js
```bash
# 安装 serve 工具
npm install -g serve

# 启动服务器
npm start
# 或
serve .

# 访问 http://localhost:3000
```

### 使用 Python
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# 访问 http://localhost:8000
```

### 使用 PHP
```bash
php -S localhost:8000

# 访问 http://localhost:8000
```

## 数据备份建议

由于数据存储在浏览器 localStorage 中，建议定期备份：

1. **定期导出** - 使用"导出图表"功能保存 JSON 文件
2. **多设备同步** - 通过导入导出在不同设备间同步数据
3. **版本控制** - 将重要图表代码保存到 Git 仓库
4. **云存储** - 将导出的 JSON 文件保存到云盘

## 致谢

- [Mermaid.js](https://mermaid.js.org/) - 强大的图表渲染引擎
- [Tailwind CSS](https://tailwindcss.com/) - 优秀的 CSS 框架
- [Mermaid Live Editor](https://github.com/mermaid-js/mermaid-live-editor) - 原始项目灵感来源
