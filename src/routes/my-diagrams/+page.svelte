<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { Button } from '$/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$/components/ui/card';
  import { Input } from '$/components/ui/input';
  import { Badge } from '$/components/ui/badge';
  import * as Dialog from '$/components/ui/dialog';
  import { Separator } from '$/components/ui/separator';
  import { toast } from 'svelte-sonner';
  import {
    myDiagramsStore,
    addDiagram,
    deleteDiagram,
    updateDiagram,
    exportDiagrams,
    importDiagrams,
    type MyDiagram
  } from '$/stores/myDiagrams';
  import { serializeState } from '$/util/serde';
  import { defaultState } from '$/util/state';
  import dayjs from 'dayjs';
  import PlusIcon from '~icons/material-symbols/add';
  import EditIcon from '~icons/material-symbols/edit';
  import DeleteIcon from '~icons/material-symbols/delete';
  import DownloadIcon from '~icons/material-symbols/download';
  import UploadIcon from '~icons/material-symbols/upload';
  import ViewIcon from '~icons/material-symbols/visibility';
  import CopyIcon from '~icons/material-symbols/content-copy';

  let diagrams: MyDiagram[] = $state([]);
  let searchTerm = $state('');
  let isCreateDialogOpen = $state(false);
  let isEditDialogOpen = $state(false);
  let editingDiagram: MyDiagram | null = $state(null);
  let newDiagramTitle = $state('');
  let newDiagramCode = $state(`graph TD
    A[开始] --> B{是否有问题?}
    B -->|是| C[解决问题]
    B -->|否| D[继续]
    C --> D
    D --> E[结束]`);

  // 订阅 store 变化
  $effect(() => {
    diagrams = $myDiagramsStore;
  });

  // 过滤图表
  const filteredDiagrams = $derived(
    diagrams.filter(diagram => 
      diagram.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      diagram.code.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const createNewDiagram = () => {
    if (!newDiagramTitle.trim()) {
      toast.error('请输入图表标题');
      return;
    }
    
    const diagram: Omit<MyDiagram, 'id'> = {
      title: newDiagramTitle.trim(),
      code: newDiagramCode,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    
    addDiagram(diagram);
    toast.success('图表创建成功');
    
    // 重置表单
    newDiagramTitle = '';
    newDiagramCode = `graph TD
    A[开始] --> B{是否有问题?}
    B -->|是| C[解决问题]
    B -->|否| D[继续]
    C --> D
    D --> E[结束]`;
    isCreateDialogOpen = false;
  };

  const editDiagram = (diagram: MyDiagram) => {
    editingDiagram = { ...diagram };
    isEditDialogOpen = true;
  };

  const saveEditedDiagram = () => {
    if (!editingDiagram || !editingDiagram.title.trim()) {
      toast.error('请输入图表标题');
      return;
    }
    
    updateDiagram(editingDiagram.id, {
      title: editingDiagram.title.trim(),
      code: editingDiagram.code,
      updatedAt: Date.now()
    });
    
    toast.success('图表更新成功');
    isEditDialogOpen = false;
    editingDiagram = null;
  };

  const deleteDiagramWithConfirm = (diagram: MyDiagram) => {
    if (confirm(`确定要删除图表 "${diagram.title}" 吗？`)) {
      deleteDiagram(diagram.id);
      toast.success('图表删除成功');
    }
  };

  const openInEditor = (diagram: MyDiagram) => {
    const state = {
      ...defaultState,
      code: diagram.code
    };
    const serialized = serializeState(state);
    goto(`/edit#${serialized}`);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('已复制到剪贴板');
    } catch (err) {
      toast.error('复制失败');
    }
  };

  const handleExport = () => {
    exportDiagrams();
    toast.success('图表数据已导出');
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    input.addEventListener('change', async (event) => {
      const file = (event.target as HTMLInputElement)?.files?.[0];
      if (!file) return;
      
      try {
        const text = await file.text();
        const importedCount = importDiagrams(text);
        toast.success(`成功导入 ${importedCount} 个图表`);
      } catch (error) {
        toast.error('导入失败，请检查文件格式');
      }
    });
    input.click();
  };

  const getDiagramType = (code: string): string => {
    const firstLine = code.trim().split('\n')[0].toLowerCase();
    if (firstLine.includes('graph')) return 'flowchart';
    if (firstLine.includes('sequencediagram')) return 'sequence';
    if (firstLine.includes('gantt')) return 'gantt';
    if (firstLine.includes('pie')) return 'pie';
    if (firstLine.includes('journey')) return 'journey';
    if (firstLine.includes('gitgraph')) return 'gitgraph';
    if (firstLine.includes('erdiagram')) return 'er';
    return 'other';
  };

  const getTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
      flowchart: 'bg-blue-100 text-blue-800',
      sequence: 'bg-green-100 text-green-800',
      gantt: 'bg-purple-100 text-purple-800',
      pie: 'bg-yellow-100 text-yellow-800',
      journey: 'bg-pink-100 text-pink-800',
      gitgraph: 'bg-indigo-100 text-indigo-800',
      er: 'bg-red-100 text-red-800',
      other: 'bg-gray-100 text-gray-800'
    };
    return colors[type] || colors.other;
  };
</script>

<svelte:head>
  <title>我的图表 - Mermaid Live Editor</title>
</svelte:head>

<div class="container mx-auto p-6 max-w-7xl">
  <!-- 头部 -->
  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
    <div>
      <h1 class="text-3xl font-bold">我的图表</h1>
      <p class="text-muted-foreground">管理和预览你的 Mermaid 图表</p>
    </div>
    
    <div class="flex gap-2">
      <Button variant="outline" size="sm" onclick={handleImport}>
        <UploadIcon class="w-4 h-4 mr-2" />
        导入
      </Button>
      <Button variant="outline" size="sm" onclick={handleExport}>
        <DownloadIcon class="w-4 h-4 mr-2" />
        导出
      </Button>
      <Button onclick={() => isCreateDialogOpen = true}>
        <PlusIcon class="w-4 h-4 mr-2" />
        新建图表
      </Button>
    </div>
  </div>

  <!-- 搜索栏 -->
  <div class="mb-6">
    <Input
      type="text"
      placeholder="搜索图表..."
      bind:value={searchTerm}
      class="max-w-md"
    />
  </div>

  <!-- 统计信息 -->
  <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
    <Card>
      <CardContent class="p-4">
        <div class="text-2xl font-bold">{diagrams.length}</div>
        <div class="text-sm text-muted-foreground">总图表数</div>
      </CardContent>
    </Card>
    <Card>
      <CardContent class="p-4">
        <div class="text-2xl font-bold">{filteredDiagrams.length}</div>
        <div class="text-sm text-muted-foreground">搜索结果</div>
      </CardContent>
    </Card>
    <Card>
      <CardContent class="p-4">
        <div class="text-2xl font-bold">
          {diagrams.filter(d => Date.now() - d.updatedAt < 7 * 24 * 60 * 60 * 1000).length}
        </div>
        <div class="text-sm text-muted-foreground">最近7天更新</div>
      </CardContent>
    </Card>
  </div>

  <!-- 图表网格 -->
  {#if filteredDiagrams.length === 0}
    <div class="text-center py-12">
      <div class="text-muted-foreground mb-4">
        {searchTerm ? '没有找到匹配的图表' : '还没有图表，创建你的第一个图表吧！'}
      </div>
      {#if !searchTerm}
        <Button onclick={() => isCreateDialogOpen = true}>
          <PlusIcon class="w-4 h-4 mr-2" />
          创建图表
        </Button>
      {/if}
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each filteredDiagrams as diagram (diagram.id)}
        <Card class="hover:shadow-lg transition-shadow">
          <CardHeader class="pb-3">
            <div class="flex justify-between items-start">
              <CardTitle class="text-lg line-clamp-2">{diagram.title}</CardTitle>
              <Badge class={getTypeColor(getDiagramType(diagram.code))}>
                {getDiagramType(diagram.code)}
              </Badge>
            </div>
            <div class="text-sm text-muted-foreground">
              创建于 {dayjs(diagram.createdAt).format('YYYY-MM-DD HH:mm')}
              {#if diagram.updatedAt !== diagram.createdAt}
                <br />更新于 {dayjs(diagram.updatedAt).format('YYYY-MM-DD HH:mm')}
              {/if}
            </div>
          </CardHeader>
          <CardContent>
            <div class="bg-muted p-3 rounded text-sm font-mono mb-4 max-h-32 overflow-hidden">
              <pre class="whitespace-pre-wrap text-xs">{diagram.code.slice(0, 200)}{diagram.code.length > 200 ? '...' : ''}</pre>
            </div>
            
            <div class="flex gap-2 flex-wrap">
              <Button size="sm" onclick={() => openInEditor(diagram)}>
                <ViewIcon class="w-4 h-4 mr-1" />
                预览
              </Button>
              <Button size="sm" variant="outline" onclick={() => editDiagram(diagram)}>
                <EditIcon class="w-4 h-4 mr-1" />
                编辑
              </Button>
              <Button size="sm" variant="outline" onclick={() => copyToClipboard(diagram.code)}>
                <CopyIcon class="w-4 h-4 mr-1" />
                复制
              </Button>
              <Button 
                size="sm" 
                variant="destructive" 
                onclick={() => deleteDiagramWithConfirm(diagram)}
              >
                <DeleteIcon class="w-4 h-4 mr-1" />
                删除
              </Button>
            </div>
          </CardContent>
        </Card>
      {/each}
    </div>
  {/if}
</div>

<!-- 创建图表对话框 -->
<Dialog.Root bind:open={isCreateDialogOpen}>
  <Dialog.Content class="max-w-4xl max-h-[80vh] overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title>创建新图表</Dialog.Title>
    </Dialog.Header>
    
    <div class="space-y-4">
      <div>
        <label for="title" class="block text-sm font-medium mb-2">图表标题</label>
        <Input
          id="title"
          bind:value={newDiagramTitle}
          placeholder="输入图表标题..."
          class="w-full"
        />
      </div>
      
      <div>
        <label for="code" class="block text-sm font-medium mb-2">Mermaid 代码</label>
        <textarea
          id="code"
          bind:value={newDiagramCode}
          placeholder="输入 Mermaid 代码..."
          class="w-full h-64 p-3 border rounded-md font-mono text-sm"
        ></textarea>
      </div>
    </div>
    
    <Dialog.Footer>
      <Button variant="outline" onclick={() => isCreateDialogOpen = false}>取消</Button>
      <Button onclick={createNewDiagram}>创建</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<!-- 编辑图表对话框 -->
<Dialog.Root bind:open={isEditDialogOpen}>
  <Dialog.Content class="max-w-4xl max-h-[80vh] overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title>编辑图表</Dialog.Title>
    </Dialog.Header>
    
    {#if editingDiagram}
      <div class="space-y-4">
        <div>
          <label for="edit-title" class="block text-sm font-medium mb-2">图表标题</label>
          <Input
            id="edit-title"
            bind:value={editingDiagram.title}
            placeholder="输入图表标题..."
            class="w-full"
          />
        </div>
        
        <div>
          <label for="edit-code" class="block text-sm font-medium mb-2">Mermaid 代码</label>
          <textarea
            id="edit-code"
            bind:value={editingDiagram.code}
            placeholder="输入 Mermaid 代码..."
            class="w-full h-64 p-3 border rounded-md font-mono text-sm"
          ></textarea>
        </div>
      </div>
    {/if}
    
    <Dialog.Footer>
      <Button variant="outline" onclick={() => isEditDialogOpen = false}>取消</Button>
      <Button onclick={saveEditedDiagram}>保存</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
