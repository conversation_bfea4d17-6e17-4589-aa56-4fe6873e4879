<script lang="ts">
  import { ToggleGroup as ToggleGroupPrimitive } from 'bits-ui';
  import { getToggleGroupCtx } from './toggle-group.svelte';
  import { cn } from '$lib/utils.js';
  import { type ToggleVariants, toggleVariants } from '$lib/components/ui/toggle/index.js';

  let {
    ref = $bindable(null),
    value = $bindable(),
    class: className,
    size,
    variant,
    ...restProps
  }: ToggleGroupPrimitive.ItemProps & ToggleVariants = $props();

  const ctx = getToggleGroupCtx();
</script>

<ToggleGroupPrimitive.Item
  bind:ref
  class={cn(
    toggleVariants({
      variant: ctx.variant || variant,
      size: ctx.size || size
    }),
    className
  )}
  {value}
  {...restProps} />
