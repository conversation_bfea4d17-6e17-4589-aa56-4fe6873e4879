{"name": "mermaid-live-editor", "version": "2.0.67", "type": "module", "license": "MIT", "scripts": {"dev": "vite dev", "dev:force": "MERMAID_LOCAL=true pnpm dev --force", "dev:test": "pnpm dev", "build": "vite build", "preview": "vite preview", "lint": "prettier --check --cache . && eslint --ignore-path .gitignore .", "lint:fix": "prettier --write --cache . && eslint --fix --ignore-path .gitignore .", "format": "prettier --write --cache .", "pre-commit": "lint-staged", "postinstall": "husky install && svelte-kit sync && (git config blame.ignoreRevsFile .git-blame-ignore-revs || true)", "test:unit": "vitest", "test:unit:ui": "vitest --ui", "test:unit:coverage": "vitest run --coverage", "test": "pnpm test:unit && pnpm test:e2e", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug"}, "devDependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@iconify-json/material-symbols": "^1.2.20", "@iconify-json/mdi": "^1.2.3", "@playwright/test": "^1.52.0", "@sveltejs/adapter-static": "3.0.8", "@sveltejs/kit": "2.20.8", "@sveltejs/vite-plugin-svelte": "^4.0.4", "@types/hammerjs": "^2.0.46", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.10", "@types/pako": "2.0.3", "@types/uuid": "9.0.8", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "@vitest/coverage-v8": "2.1.9", "@vitest/ui": "^2.1.9", "autoprefixer": "^10.4.21", "bits-ui": "^1.4.6", "c8": "7.14.0", "chai": "^4.5.0", "clsx": "^2.1.1", "cssnano": "^6.1.2", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-no-only-tests": "^3.3.0", "eslint-plugin-postcss-modules": "^2.0.0", "eslint-plugin-sort-keys": "^2.3.5", "eslint-plugin-svelte": "^2.46.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-plugin-vitest": "^0.5.4", "esserializer": "^1.3.11", "husky": "^8.0.3", "jsdom": "^25.0.1", "lint-staged": "^15.5.1", "lucide-svelte": "^0.507.0", "node-html-parser": "^6.1.13", "paneforge": "^1.0.0-next.5", "postcss": "^8.5.3", "postcss-load-config": "5.1.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.28.2", "svelte-preprocess": "^6.0.3", "svelte-sonner": "^0.3.28", "tailwind-merge": "^3.2.0", "tailwind-variants": "^0.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tslib": "^2.8.1", "typescript": "^5.8.3", "unplugin-icons": "^22.1.0", "vite": "^5.4.19", "vitest": "^2.1.9", "vitest-dom": "^0.1.1"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-yaml": "^6.1.2", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.7", "@fontsource-variable/recursive": "^5.2.5", "@fsegurai/codemirror-theme-vscode-dark": "^6.1.4", "@fsegurai/codemirror-theme-vscode-light": "^6.1.4", "@mermaid-js/layout-elk": "^0.1.7", "@mermaid-js/mermaid-zenuml": "^0.2.0", "codemirror": "^6.0.1", "dayjs": "^1.11.13", "hammerjs": "^2.0.8", "js-base64": "3.7.7", "lodash-es": "^4.17.21", "mermaid": "^11.6.0", "mode-watcher": "^0.5.1", "monaco-editor": "0.52.2", "pako": "2.1.0", "plausible-tracker": "^0.3.9", "random-word-slugs": "0.1.7", "svg-pan-zoom": "3.6.2", "svg2roughjs": "^3.2.1", "uuid": "9.0.1"}, "lint-staged": {"*.{ts,svelte,js,css,md,json}": ["prettier --plugin-search-dir=. --write", "eslint --ignore-path .gitignore "]}, "engines": {"node": ">=20.19.0"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "pnpm": {"onlyBuiltDependencies": ["deas<PERSON>", "esbuild", "svelte-preprocess"], "ignoredBuiltDependencies": ["vue-demi"]}}